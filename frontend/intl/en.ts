import { routes } from '@/config/routes';

export const en = {
  account: {
    account: 'Account',
    accountInformation: 'Account Information',
    accountOptions: 'Account Options',
    accountSettings: 'Visit account settings',
    changeAccountPassword:
      'To change your password, verify your current password and enter a new one below.',
    changePassword: 'Change Password',
    changesSaved: 'Changes saved',
    clickLink: 'Click the link sent to',
    clickTheConfirmation: 'Click the confirmation link we sent to',
    codeRequired: 'You must agree to the Terms of Use.',
    completeProfileText: 'Complete your profile to access favorites.',
    completeYourProfile: 'Complete Your Profile',
    completeYourProfileModal: 'Complete Your Profile Modal',
    confirmEmail: 'Confirm Email',
    confirmEmailMessage: 'Please confirm your email address to finish setting up your account.',
    confirmNewPassword: 'Confirm new password',
    confirmPassword: 'Confirm Password',
    createYourAccount: 'Create Your Free TRAILS Account',
    createYourProfile: 'Create your profile',
    didntReceiveEmail: "Didn't receive an email?",
    downloadHistory: 'Download History',
    downloadHistoryNote: 'Note that any downloads before August 23, 2024 will not appear.',
    email: 'Email',
    emailAddress: 'Email Address',
    emailAlreadyConfirmed: 'Email address has already been confirmed.',
    emailAlreadyConfirmedHeader: 'Email already confirmed',
    emailConfirmation: 'Email confirmation sent!',
    emailConfirmationLinkDetails:
      "Please provide your email address and we'll send a link for you to confirm your email.",
    emailConfirmed: 'Email confirmed!',
    emailConfirmedMessage:
      'Thank you for confirming your email address. Log in below to start exploring TRAILS materials.',
    emailHint: 'If you work at a school or mental health agency, please use your work address.',
    emailRequired: 'Email address is required.',
    emailUsed: 'This email address is already in use.',
    enterCurrentPassword: 'Enter current Password',
    enterEmail: 'Enter email:',
    enterNewPassword: 'Enter a new password below.',
    enterValidEmail: 'Please enter a valid email address.',
    expiredPasswordDetails:
      "The password recovery link you used has expired. Enter your email address below and we'll send you a new one.",
    expiredPasswordHeader: 'Expired link',
    finalStep:
      'This is the final step! You can update any of the information provided at any time in your account settings.',
    findYourDownloadHistoryHere: 'Find your Download History here.',
    forgotPasswordDetails:
      "Enter your email address below and we'll send a link to help you recover it.",
    forgotYourPassword: 'Forgot your password?',
    iHaveRead: 'I have read and accept the TRAILS',
    invalidPassword: "Password doesn't meet the requirements.",
    keepMeLoggedIn: 'Keep me logged in',
    learnMoreAboutYourDownloadHistory: 'Learn more about your Download History',
    logIn: 'Log in',
    logInInstead: 'Log in instead',
    logInToYourAccout: 'Log in to your account',
    logOut: 'Log out',
    newPassword: 'New password',
    noEmailAccount: "There isn't an account for this email.",
    password: 'Password',
    passwordChanged: 'Password changed',
    passwordHint: 'Must contain at least 8 characters, a capital letter, and a number.',
    passwordIncorrect: 'Incorrect password.',
    passwordRecoveryLinkSent: 'Password recovery link sent!',
    passwordRequired: 'Password is required.',
    passwordSaved: 'Password Saved!',
    passwordsNotMatch: 'Passwords do not match.',
    passwordSuccessfullyUpdated: 'Your password has been successfully updated.',
    pwdsMatch: 'Passwords match.',
    pwdSuccess: 'Password meets requirements.',
    redownloadAllUpdatedMaterials: 'Redownload all updated materials',
    resendEmail: 'Resend email',
    resendEmailConfirmationLink: 'Resend your email confirmation link',
    returnToLogin: 'Return to log in',
    saveChanges: 'Save Changes',
    savingChanges: 'Saving changes...',
    selectAccountOption: 'Select account option',
    signUp: 'Sign up',
    signUpForAccount: 'Create Your TRAILS Account',
    signUpInstead: 'Sign up instead',
    signUpToTrails: 'Sign up to access TRAILS materials',
    termsAndConditions: 'Terms and Conditions',
    termsOfUse: 'Terms of Use',
    toFinish: 'to finish setting up your account.',
    toVerify: 'to verify your account.',
    updateYourPassword: 'Update your password',
    verifyLinkUsedBody:
      "You've already done this step! Log in below to start exploring TRAILS materials.",
    youAreIn: "You're in! Let's confirm your email.",
    youHaventDownloadedAnyMaterialsYet: 'You haven’t downloaded any materials yet',
    yourAccount: 'Your Account',
    yourDownloadHistory: 'Your Download History',
  },

  coaching: {
    adaptAndSend: 'Adapt and send the',
    agendasCovered: 'CBT and Mindfulness Agendas Covered',
    allEmailsTrails:
      'All emails from the TRAILS website will <NAME_EMAIL>',
    checkingWithAdmin: 'Check with your administrator',
    checkYourSpam: 'Check your spam folder',
    coachEmailTemplateDescription:
      "Use these email templates to help keep your school mental health professionals (SMHPs) on track, moving forward, and ready to lead their groups. Be sure to <strong>review and edit the text before sending</strong>, as it may require adjustments to better suit your SMHPs' performance, community context, or method of program delivery.",
    coaching: 'Coaching',
    coachInstructionsHeader: 'In-Session Practice Coach Instructions',
    doubleCheckYouHaveSignedUp:
      'Double-check that you have signed up for TRAILS with the correct email address. Typos in email addresses are common – for example, typing ".com" instead of ".org." If you think you may have signed up for TRAILS with the wrong email address, please <a href="/sign-up">sign up again</a> with your correct address, and reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a> to let us know what happened.',
    emailsCanGetCaughtInSpam: `Emails from TRAILS can sometimes get caught in spam filters. Check the spam folder in your email inbox to see if your account confirmation or password reset email is in there. If you can\'t find the email in your local spam folder, ask your network administrator if the message might be blocked at the network level. If your administrator needs additional information, our <a href="${routes.aboutContact()}">support team</a> can provide the correct IP address to allow.`,
    familyLetters: 'family letters',
    followUpEmailTemplate: 'Follow-Up Email Template',
    forEstablishedSchedules: 'FOR ESTABLISHED SCHEDULES:',
    forEstablishedSchedulesDescription:
      "If you already provided TRAILS with your availability and received an email confirming your group's regular meeting time, please review:",
    ifYouTriedTheRecommendations: `If you\'ve tried the recommendations above and still can\'t receive emails from TRAILS, you may need to contact your administrator to check if your domain is reachable, or if our emails are being blocked at a network level. Administrators can reach out to our <a href="${routes.aboutContact()}">support team</a> for more information.`,
    inSessionPracticeMaterials: 'In-Session Practice Materials',
    inSessionPracticeMaterialsDescription:
      "During this {type} session, have SMHPs sign up for next session's practice using the sign-up sheet. Feel free to review the instructions in preparation for next session.",
    introductoryEmailTemplate: 'Introductory Email Template',
    lccEmailTemplatesHeader: 'Learning Collaborative Coaching Email Templates',
    notReceivingEmail:
      'Not receiving account confirmation emails or password reset emails from TRAILS?',
    openThe: 'Open the',
    pleaseConsiderAddingDomain:
      'Please consider adding the domain <a href="https://trailstowellness.org">trailstowellness.org</a> to your address book and/or asking your network administrator to allow emails from this domain. If you\'re using a personal email address (such as Hotmail, Gmail, or Outlook) and are having trouble receiving your confirmation email, try adding <a href="mailto:<EMAIL>"><EMAIL></a> to your contacts list.',
    remindSMHPs: 'Remind SMHPs to adapt and send out the',
    resendConfirmationEmail: 'Resend your TRAILS Confirmation Email',
    resendPasswordResetEmail: 'Resend your TRAILS Password Reset Email',
    resendTrailsEmail: 'Resend a TRAILS email',
    reviewThe: 'Review the',
    smhpInstructionsHeader: 'In-Session Practice SMHP Instructions',
    sometimesEmailFalls:
      "Sometimes emails fall through the cracks and don't quite make it to your inbox. If you're not receiving emails from us, try following the steps below.",
    theEmailWillComeFrom: 'The email will <NAME_EMAIL>.',
    toConfirmScheduling: 'to Confirm Scheduling',
    toDetermineScheduling: 'TO DETERMINE SCHEDULING:',
    toDetermineSchedulingDescription:
      'If you are assigned to specific SMHPs and will be scheduling with them directly, please review:',
    trailsDashboardSupport: 'TRAILS Dashboard Support',
    updateThe: 'Update the',
    week1EmailTemplate: 'Week 1 Email Template',
  },

  common: {
    acceptAndClose: 'Accept and Close',
    actions: 'Actions',
    back: 'Back',
    cancel: 'Cancel',
    changeLesson: 'Change Lesson',
    changeSession: 'Change Session',
    changeWeek: 'Change Week',
    city: 'City',
    close: 'Close',
    colorado: 'Colorado',
    confirm: 'Confirm',
    copyTemplateText: 'Copy Template Text',
    country: 'Country',
    county: 'County',
    day: 'Day',
    dateDownloaded: 'Date Downloaded',
    download: 'Download',
    downloadWordDoc: 'Download Word Doc',
    dropdownMenuFor: 'A dropdown menu for: {name}',
    emailSent: 'Email sent!',
    english: 'English',
    getTheNewVersion: 'Get the new version',
    hideMaterials: 'Hide all included materials',
    learnMore: 'Learn more about our work',
    learnMoreAdaptableAgendasBeta: 'Learn more',
    learnMoreFooter: 'Learn More',
    like: 'Like',
    linkCopied: 'Link copied!',
    loadMore: 'Load more',
    massachusetts: 'Massachusetts',
    material: 'Material',
    maxCharactersExceeded: 'Maximum Characters exceeded',
    michigan: 'Michigan',
    minutes: 'minutes',
    name: 'Name',
    next: 'Next',
    noResults: 'No results found',
    openPdf: 'Open PDF',
    district: 'District',
    organization: 'Organization',
    popularSearches: 'POPULAR SEARCHES',
    previous: 'Previous',
    printThisPage: 'Print this page',
    province: 'Province',
    recentResources: 'RECENTLY OPENED RESOURCES',
    recentSearches: 'RECENT SEARCHES',
    redownload: 'Redownload',
    redownloadAll: 'Redownload all',
    school: 'School',
    showMaterials: 'Show all included materials',
    search: 'Search',
    spanish: 'Spanish',
    state: 'State',
    submit: 'Submit',
    textCopied: 'Text Copied!',
    uploading: 'Uploading...',
    uploadPicture: 'Upload picture',
    welcome: 'Welcome, {name}!',
    zipping: 'Zipping',
  },

  curriculum: {
    adaptableAgendasBeta: 'Adaptable Agendas Beta',
    adaptingAndReinforcingTheLesson: 'Adapting and reinforcing the lesson',
    alternativeResources: 'Alternative resources for',
    altResourcesCollection: 'Alternative resource collection',
    altResourcesDescription:
      'Activities in this unit can be customized to better fit the needs of your students. To swap out a lesson activity, choose from the suggestions below.',
    associatedVideo: 'associated video',
    cbtResourcesInSession: 'Resources in Session',
    changeGradeBand: 'Change Grade Band',
    coachingThisSession: 'Coaching this session',
    coachVideo: 'Coach video',
    currentGrade:
      "You're currently viewing the {currentGrade}. Select a different grade band below.",
    curriculum: 'Curriculum',
    downloadSelStarterPack: 'Download Starter Pack',
    enhancingStudentEngagement: 'Enhancing student engagement',
    grade: 'Grade',
    grades: 'Grades',
    iHaveCompletedThisLesson: "I've Completed This Lesson",
    jumpIntoMaterials:
      "As you make your way through the {type}, you'll be able to track your progress here.",
    lccResourcesInAgenda: 'Resources in Coaching Agenda',
    lesson: 'Lesson',
    lessonAgenda: 'Lesson Agenda',
    lessons: 'Lessons',
    lookingForTheSelLessons: 'Looking for the SEL lessons?',
    needAlternativeActivities: 'Need alternatives to the activities in this unit?',
    objectives: 'Objectives',
    optOutAdaptableAgendasBeta: 'Return to the standard lesson view',
    postGroupChecklist: 'Post-Group Checklist',
    postSession: 'Post-Session',
    preGroupChecklist: 'Pre-Group Checklist',
    preSession: 'Pre-Session',
    printResourcesButtonTextCBTM: 'Download all resources to print',
    printResourcesDescriptionCBTM:
      "For these materials you'll need to print several copies to hand out to students. Please review the session PDF for additional instruction.",
    resourcePdFs: 'Resource PDFs',
    returnToTheBeginning: 'Return to the beginning of the curriculum by opening a lesson below.',
    sel: 'Social and Emotional Learning',
    selCurriculum: 'SEL Curriculum',
    selIsAnEasyIntroduction:
      'The Social and Emotional Learning (SEL) Starter Pack is a collection of free SEL resources for teachers, parents, and caregivers.',
    selLessonsAreShared:
      'SEL lessons are shared with partner schools only. Interested in becoming a partner? Already been trained and missing access? Reach out. Content please write something better.',
    selPageHeaderSubtitle:
      '20 brief lessons in each curriculum with a range of 3-6 lessons per unit',
    selResourcesInLesson: 'Resources in Lesson',
    selStarterPack: 'SEL Starter Pack',
    selTextBlockBody:
      "In any age group, no two classrooms are alike. We recommend starting with the grade band that best represents your students' overall developmental level, and swapping out individual lessons or activities as needed. The lesson and unit structure are the same for all grades to make customization as seamless as possible.",
    selTextBlockHeader: 'Not sure which grade band is right for you?',
    session: 'Session',
    sessionAgenda: 'Session Agenda',
    toDeliverToSMHPs: 'to deliver this material to your SMHPs',
    unit: 'Unit',
    unitAltResources: 'unit alternative resources',
    unitContains: 'unit contains',
    unitLearningObjective: 'unit learning objective',
    units: 'Units',
    viewAllCBTComponent: 'View all CBT Component Delivery Series videos',
    viewedAnything: "Looks like you're just getting started",
    viewLesson: 'View Lesson',
    whatYouWillNeed: "What You'll Need",
    youReachedTheEnd: "Congrats! You've reached the end of the {subject}.",
  },

  errors: {
    buttonTextPrograms404: 'View programs overview',
    descPrograms404:
      'We help schools provide evidence-based mental health programming across three tiers of service.',
    descQuestion404: 'Need more information about TRAILS?',
    descStory404:
      "Since 2013, we've been pioneering new interventions in school-based mental health.",
    error403ContactDesc:
      'If you believe you should have access to this page, reach out to us using our <a href="/about/contact">contact form</a>.',
    error403HeadingDesc: "You don't have permission to view this page.",
    error403HeadingLabel: '403 Error',
    error403HeadingTitle: 'Access limited',
    error403ProblemDesc:
      'If the problem persists, please <a href="https://airtable.com/appOQo14sy3HcIa8I/shrGunzBgqey8Xv52">report your problem</a> and note this error message and the query that caused it.',
    error500ContactDesc:
      'If you need to access something from this page ASAP, please reach out to us using our <a href="/about/contact">contact form</a>.',
    error500HeadingDesc: 'The server encountered an error and cannot complete your request.',
    error500HeadingLabel: '500 Error',
    error500HeadingTitle: 'Something went wrong',
    error500ProblemDesc:
      '<a href="https://airtable.com/appOQo14sy3HcIa8I/shrGunzBgqey8Xv52">Let us know</a> you experienced an issue. Please note this error message and the type of information you were trying to access.',
    invalidCountry: 'Invalid country',
    invalidCounty: 'Invalid county',
    invalidState: 'Invalid state',
    reportAProblem: 'Report a Problem',
    selStarterDesc404:
      'Get to know TRAILS Social and Emotional Learning through this collection of free, easy-to-use resources.',
    selStarterHeading404: 'SEL Starter Pack',
    suicideDesc404:
      'Find tools to educate students about suicide, share lifelines, or connect with local crisis services.',
    suicideHeading404: 'Suicide Prevention Resources',
    surveyNotAvailableHeading: 'Need access?',
    surveyNotAvailablePageDescription: "You don't have permission to access this survey.",
    surveyNotAvailablePageHeading: 'Survey Not Available',
    surveyNotAvailableText:
      'Please <a href="/about/contact">contact us</a> to let us know if you believe you should have access to the ',
    tierOneSelDesc404:
      'Explore the full curriculum for grades K-12, including lesson plans, worksheets, implementation resources, and more.',
    tierOneSelHeading404: 'Social and Emotional Learning',
    tierTwoCbtDesc404:
      'Explore a wide library of resources designed to help students cope with depression and anxiety symptoms.',
    tierTwoCbtHeading404: 'CBT and Mindfulness Resources',
    titlePrograms404: 'Our programs',
    titleQuestion404: 'Ask a question',
    titleStory404: 'Our story',
  },

  feedback: {
    anyFeedback: 'Any feedback on this',
    anyFeedbackOnThisLesson: 'Any feedback on this lesson?',
    anyFeedbackOnThisSession: 'Any feedback on this session?',
    byCompletingThis:
      "By completing this optional form, you'll help us get a better sense of who uses free TRAILS materials.",
  },

  forms: {
    careersAtTrails: 'Careers at TRAILS',
    completeSurvey: 'Send Us a Message',
    contactForm: 'Connect with TRAILS',
    contactFormIntro:
      'Looking to ask a question about TRAILS, explore partnership opportunities, or share feedback? Reach out to us here.',
    copySurveyLink: 'Copy survey link',
    downloadFamilyLetter: 'Download Family Letter',
    downloadFamilyLetterModal: 'Download Family Letter Modal',
    downloadFamilyUnitLetter: 'Download Unit Family Letter',
    downloadIntroFamilyLetter: 'Download Introductory Family Letter',
    downloadUnitFamilyLetter: 'Download Unit {number} Family Letter',
    emailCopy: 'Email me a copy of my responses',
    emailTemplate: 'Email Template',
    familyLetter: 'Family Letter',
    familyLetterInstructions:
      "Please be sure to <strong>review and edit</strong> the text before sending, as there may be specific adjustments you'd like to make.",
    goToSurvey: 'Go to survey',
    informationAboutTrails: 'Information about TRAILS programming',
    informationForFunders: 'Information on becoming a funder',
    informationHowToSupport:
      'Information on how to support school partners by becoming a TRAILS Coach',
    informationSchoolPartner: 'Information on becoming a school partner',
    invalidSubject: 'Please select a subject from the list below.',
    isRequired: '{name} is required.',
    message: 'Message',
    otherCollaboration: 'Other collaboration opportunity',
    pleaseReachOut:
      'Your name, email, and school/organization are tied to your partnership with TRAILS, please reach out to <a href="mailto:<EMAIL>"><EMAIL></a> to have your name or email updated.',
    returnToYourSurveys: 'Return to your surveys',
    selectAllOfTheLanguages: "Select the language(s) you'd like to download below.",
    sendMessage: 'Send Message',
    skillAndFeedbackNotes: 'Skill and Feedback Notes',
    subject: 'Subject',
    thankYouForCompletingSurvey: 'Thank you for completing this survey!',
    toGetStarted: 'To get started, tell us about the needs of your school or district.',
    websiteHelp: 'Website help or feedback',
  },

  notifications: {
    changeNotificationsPreferences: 'Change notifications preferences',
    coachAnnouncementHeader: 'Coach Announcement',
    emailedNotifications: 'Get notifications in your inbox each week',
    emailNotifications: 'Email me my notifications each week',
    lccCoachingAnnouncementContent:
      'The TRAILS team has made some new updates specifically for coaches! You can now find all Learning Collaborative materials – including agendas, resources, and slide decks – in their own section under the <b>Materials</b> tab at the top of this page.',
    lccCoachingAnnouncementHeader: 'Learning Collaborative Coaching materials are available now',
    legacyCoachAnnouncementContent:
      "You'll now be able to access your weekly coaching log using the new <b>Surveys</b> tab at the top of this page. Here, you'll find your coaching logs for each week, as well as a collection of handy survey links to share with your SMHPs. If you're currently assigned to an SMHP, remember to log your coaching every week, even if just to report that no coaching was delivered.",
    legacyCoachingAnnouncementHeader: "We've added a new feature to make coach logging easier",
    markAllAsRead: 'Mark all as read',
    messageSent:
      'Message sent! You will receive a response in 1-3 business days.<br/>Review your message below.',
    noNewNotifications: 'No new notifications',
    notifications: 'Notifications',
    notificationsPreferencesSaved: 'Notification preferences saved',
    optedIntoNotifications: 'You are currently opted into weekly notification wrap-up emails.',
    optedOutOfNotifications: 'You are not currently opted into weekly notification wrap-up emails.',
    settings: 'Settings',
    smhpAnnouncementContent:
      "TRAILS collects survey responses to better understand your experience with our programs. On the <strong>Surveys</strong> tab at the top of this page, you'll find a list of all surveys assigned to you. Please complete each survey on time to help us improve the resources and support we provide.",
    smhpAnnouncementHeader: "You've been assigned your first survey",
    viewAllNotifications: 'View All Notifications',
    viewAllNotificationsWithUnread: 'View all notifications ({unreadCount} unread)',
    visitAccountSettings:
      'Opt in using your account settings to receive your notifications in a weekly roundup email.',
    youreAllCaughtUp: "You're all caught up! Take a mindfulness moment.",
  },

  profile: {
    acceptableFileTypes: 'Accepted file types: .jpg, .jpeg, .png. Max 2MB file size.',
    addProfilePicture: 'Add a profile picture',
    approveOrEditInfo:
      'Approve or edit the information below to help us bring your profile up to date.',
    availableLanguages: 'Available Languages',
    changeCurrentGradeLevels: 'Change current grade levels',
    changeLanguagePreferences: 'Change language preferences',
    changeProfilePicture: 'Change profile picture',
    changeRole: 'Change role',
    coach: 'Community-Based Mental Health Provider',
    coachDescription: 'You provide mental health services to a broader community.',
    country: 'Country is required',
    currentGradeLevels: 'Current grade levels',
    describe: 'Describe role:',
    doNotWorkWithStudents: 'If you do not work with students, click next.',
    fName: 'First Name',
    fNameRequired: 'First name is required.',
    gradeLevelNote: 'These are the grade levels you indicated you currently work with.',
    gradeLevels: 'Grade Levels',
    iAmA: 'I am a...',
    ifOther: 'If you selected "Other," please describe:',
    invalidRole: 'Please select a role from the list below.',
    isThisInformationUpToDate: 'Is this information up to date?',
    languageNote:
      "These are the languages you indicated you're most interested in seeing resources for.",
    languageOtherRequired: 'Please describe your preferred language',
    languagePreferences: 'Language preferences',
    languages: 'Languages',
    languagesAlreadyAvailable: '*Languages already available.',
    lName: 'Last Name',
    lNameRequired: 'Last name is required.',
    newSemester: "It's a new semester!",
    noGradeLevelsIndicated: "You haven't indicated which grade levels you currently work with.",
    noGradeLevelsSelected: 'No grade levels selected',
    noInformationToShow: 'No school or organization information to show.',
    noLanguagePreferencesIndicated: "You haven't selected any language preferences.",
    noLanguagesSelected: 'No languages selected',
    noRoleIndicated: "You haven't indicated which role describes you best.",
    oneMoreStep: 'One more step!',
    other: 'Other',
    otherDescription: 'Do you work for a foundation? Media entity? Are you interested in TRAILS?',
    otherRequired: 'Please describe your role.',
    profile: 'Profile',
    profilePicture: 'Profile picture',
    removeProfilePicture: 'Remove profile picture',
    role: 'Role',
    roleCommunityMentalHealthPro: 'Community mental health professional',
    roleNote:
      'This is the role you indicated describes you best. Your role does not impact your access to materials.',
    roleOther: 'Other',
    roleParent: 'Parent',
    rolePolicymaker: 'Policymaker or legislator',
    roleRequired: 'Role is required.',
    roleSchoolAdmin: 'School or district administrator',
    roleSchoolMentalHealthPro: 'School mental health professional',
    roleSocialImpact: 'Social impact investor / Foundation',
    roleTeacher: 'Teacher',
    schoolMentalHealthProvider: 'School Mental Health Provider',
    selectLanguages: 'Select Languages',
    selectYourRole: 'Select the role that describes you best',
    smhpDescription:
      'You work with students individually and in small group settings to support their mental health.',
    teacherDescription:
      'You teach entire classes of students in a particular subject. You do not have training in mental health.',
    uploadPictureDescription: 'Upload a profile picture to help identify you in the app.',
    uploadPictureHelperText: 'This will be used to identify you in the app.',
    viewAllLanguages: 'View all languages',
    whichGradeLevels: 'Which grade levels do you currently work with? Check all that apply.',
    whichLanguages: 'Which translations would be most useful to your students and their families?',
    willNotLimitMaterials: 'Your selection will not limit the materials you can access.',
  },

  resources: {
    additionalResources: 'Additional Resources',
    additionalResourcesDescription:
      'Social and Emotional Learning resource collections designed by the TRAILS clinical team.',
    agendaResult: 'Agenda Result',
    agendaResults: 'Agenda Results',
    applyFilters: 'Apply filters',
    backManualsResources: 'Back to manuals and resources',
    cbtAndMindfulness: 'CBT and Mindfulness',
    coachingAgendas: 'Coaching Agendas',
    downloadManualOverview: 'Download Manual Overview',
    filterByGradeLevel: 'Filter by grade level',
    filterByResourceFormat: 'Filter by resource format',
    filterResults: 'Filter Results',
    freeCbtAndMindfulnessMaterials:
      'Free CBT and mindfulness materials for addressing depression and anxiety in students, including manuals and resources to run skills groups in the school setting.',
    googleSlideDeck: 'Google Slide Deck',
    howToUseTitle: 'How to use this resource',
    implementationResources: 'Implementation Resources',
    implementationResourcesDescription:
      'Tools to help you strengthen your delivery of the TRAILS SEL curriculum.',
    manual: 'Manual',
    manuals: 'Manuals',
    materials: 'materials',
    nextSlide: 'Next slide',
    previousSlide: 'Previous slide',
    programMaterials: 'Program Materials',
    resourceResult: 'Resource Result',
    resourceResults: 'Resource Results',
    resources: 'Resources',
    resourcesMoved: 'These resources have moved.',
    resourcesMovedDescription:
      'Find them in our {link} section by hovering over "Materials" at the top of your screen.',
    resourcesNoMatch: "Sorry! There aren't any resources that match your selection.",
    resourceTypes: 'Resource Types',
    result: 'result',
    results: 'results',
    searchResults: 'Search Results',
    sections: 'Sections',
    selectCategory: 'Select a category',
    suicidePrevention: 'Suicide Prevention and Risk Management',
    tryChecking: 'Try checking out another category.',
    viewAllSELCollections: 'View All SEL Additional Resources',
    viewAllSuicidePreventionMaterials: 'View All Suicide Prevention Materials',
  },

  siteContent: {
    aboutUs: 'About Us',
    accessingMaterials: 'Access Materials',
    addressingCrisis: 'Addressing the youth mental health crisis',
    afterInitialTraining:
      'TRAILS is transforming the way youth mental health care is delivered. After initial training and implementation support from us, our partner schools are able to sustain TRAILS programming for years to come.',
    backToDashboardLink: 'Back to your dashboard',
    careers: 'Careers',
    careersBody: `TRAILS collaborates – with our school partners, and with each other. By pooling our knowledge and skills across many fields, from clinical mental health to education to technology, we're able to provide effective, culturally responsible solutions for the students and schools we serve.<br/><br/>
    Our team spans the United States, with most staff working remotely (though many of us are based in Ann Arbor, Michigan, where TRAILS got its start). We offer remote and hybrid work options, competitive benefits, and generous vacation and wellness days to foster a supportive and fulfilling work environment for every member of our staff.`,
    categories: 'Categories',
    changeSiteMapSection: 'Change Sitemap Section',
    clinical: 'Clinical',
    clinicalPrograms: 'Clinical Programs',
    closing: 'Closing',
    comeWorkWithUs: 'Come work with us!',
    commonTrailsTerms: 'Common TRAILS Terms',
    communicationsAndTraining: 'Communications and Training',
    connectingWithTrails: 'Connecting with TRAILS',
    contactUs: 'Contact Us',
    cookiesAlertMessage:
      'We use cookies to better understand and improve your experience with our website. By continuing to use this site, you agree to <a href="/privacy-policy" target="_blank" rel="noopener noreferrer">our use of cookies</a>.',
    createAccount:
      'Sign up to access all Tier 2 program materials, as well as select resources at Tiers 1 and 3.',
    crisisTextLine:
      '<a href="https://www.crisistextline.org/">Crisis Text Line</a> <i>-</i> text "HOME" to 741741',
    crisisTextLine2: 'Crisis Text Line',
    currentJobOpportunities: 'Current openings',
    dashboard: 'Dashboard',
    discoverTrailsDifference: "Discover how we're making a difference",
    districtAdmin: 'District Administrator',
    doWeWantToIncludeText: 'Do we want to include any additional text here? - Q for content',
    easilyGetBack: 'Easily get back to the materials you frequently grab',
    easyAccessToResources:
      'Use this space to keep track of the resources you return to most often.',
    evaluation: 'Evaluation',
    evaluationAndResearch: 'Evaluation and research',
    executiveLeadership: 'Executive Leadership',
    exploreTheManuals: 'Explore the MANUALS',
    faq: 'FAQ',
    faqExpanded: 'Frequently Asked Questions',
    favorites: 'Favorites',
    favoritesNoEmailMessage: 'Confirm your email to begin adding favorites.',
    favoritesNoEmailTitle: 'Want to access this feature?',
    finance: 'Finance',
    financeAndOperations: 'Finance and Operations',
    forSchoolStaff: 'For school staff and mental health professionals',
    forStudents: 'For students, families, community members',
    forStudentsInNeed:
      'For students in need of mental health services, school-delivered programs can be life-changing and life-saving. TRAILS offers the training, materials, and implementation support schools need to provide their students with three tiers of effective, culturally responsible programming.',
    getHelpNow: 'Get help now',
    goToDashboard: 'Go to Dashboard',
    gradeBandContains: 'grade band contains',
    hereAreAFewMaterials:
      'Check out our most commonly used resources below, or head to the <a href="/materials/cbt-and-mindfulness/resources/launching-a-group">Launching a Group</a> section of the site for more.',
    howToPartner: 'How to partner with us',
    howWeWork:
      'The TRAILS team is working diligently to bring on new school partners. While our capacity for partnerships is greatest in Michigan, Colorado, and Massachusetts, we welcome potential partners outside of these states to reach out to us so we can reconnect when TRAILS expansions occur.',
    implementation: 'Implementation',
    interestedInPartnership: 'Interested in TRAILS partnerships?',
    jumpBackIn: 'Jump back in',
    jumpIntoMaterialsAndFavoriteResources:
      'Head over to the {materials} and click the blue heart under a resource to see it displayed here.',
    learnMoreAboutOurTeams: 'Learn more about our teams',
    learnMoreAboutProgramming: 'Learn more about TRAILS programs',
    learnMoreAboutTrails: 'Learn more about TRAILS partnerships',
    linkedIn: 'LinkedIn',
    lookingForFreeMaterials: 'Looking for our free materials?',
    moreResearchAndEvaluation: 'More Research and *Evaluation*',
    navigateToAnySectionBelow: 'Navigate to any page within this program below.',
    openTheLastSessionAgenda:
      "Return to your most recent session, see what's coming next, or revisit past sessions.",
    ourCommitments: 'Our commitments to diversity, equity, and inclusion',
    ourImpact: 'Our Impact',
    ourImpactDescription:
      "More than 10,000 school staff and mental health professionals have accessed TRAILS trainings and resources – and that's only the beginning. Alongside our partner schools, we're working to offer effective, culturally responsible programming to students nationwide.",
    ourMission: 'Our mission',
    ourPrimaryInitiatives:
      'Our primary initiatives are within Michigan, Colorado, and Massachusetts. Through our partnerships, more than 10,000 school staff and mental health professionals have accessed TRAILS trainings and resources to support their students.',
    ourPrograms: 'Our Programs',
    ourProgramsDescription:
      'TRAILS programs bring gold-standard mental health strategies to the school setting.',
    ourTeam: 'Our Team',
    overviewOfTiers: 'The TRAILS 3-tiered model',
    overviewOfTiersDescription:
      'With training, materials, and implementation support from us, staff at our partner schools can provide their students with effective programming across 3 tiers of service. While each TRAILS tier is designed to meet a different set of needs, all are grounded in the same research-driven approach. By offering the 3 tiers together, schools can promote the emotional health of all students, while better identifying and supporting those with existing mental health concerns.',
    partnershipDescription:
      'When TRAILS takes on a new school partner, we collaborate with its leaders to identify the right programming based on the needs of their staff and students. Staff at our partner schools receive training from the TRAILS clinical team, access to all materials for their contracted programming, and ongoing professional development opportunities in the form of weekly Q&As, refresher trainings, newsletters, and more.',
    partnerships: 'Partnerships',
    partnersQuote:
      '"Working with TRAILS has been one of the most meaningful collaborations I\'ve been involved with at the district in my 26 years. From the beginning, TRAILS staff demonstrated a genuine desire to learn about our district, listen to and prioritize our goals, and build upon our strengths."',
    prepareToCoachAGroup: 'Preparing to coach',
    privacyPolicy: 'Privacy Policy',
    product: 'Product',
    productAndSupport: 'Product & Support',
    programs: 'Programs',
    programsNote:
      'TRAILS aligns with the Multi-Tiered System of Supports (MTSS) framework – an academic, social, and behavioral growth model supported by the Every Student Succeeds Act and widely used in schools throughout the country.',
    reportAProblem: 'Report a Problem',
    researchAndDevelopment: 'Research and Development',
    schoolBased: 'The case for school-based mental health',
    schoolBaseDesc:
      '75% of youth who access mental health care do so in the school setting. <br/><br/> TRAILS helps school staff meet students where they are with effective, skills-based support. ',
    schoolMentalHealthProfessional: 'School Mental Health Professional',
    shapeOurProduct: 'Shape Our Product',
    siteMap: 'Site Map',
    suicideAndCrisisLifeline:
      '<a href="https://988lifeline.org/">Suicide and Crisis Lifeline</a> <i>-</i> call 988 or <a href="https://988lifeline.org/chat">chat</a>',
    suicidePreventionLifeline: 'Suicide Prevention Lifeline',
    support: 'Support',
    teamDescription:
      "TRAILS has always been a deeply collaborative effort. Our staff works together to synthesize a broad range of expertise, including clinical mental health, technology, and implementation science – and that's only the beginning.",
    teamHeading: 'Our Experienced Team',
    technologyInnovations: 'Technology Innovations',
    thePrograms: 'The Programs',
    theTrailsProgram:
      "\"The TRAILS program, it's managing to do something that's never been done before – it's coming into the schools, they're giving these kids the tools and the language to understand it all – and it's truly changing the future for our children.\"",
    tier: 'Tier',
    trails: 'TRAILS',
    trailsLogo: 'TRAILS Logo',
    trailsMaterials: 'TRAILS Materials',
    trailsPartnerships: 'TRAILS Partnerships',
    trailsPrograms: 'TRAILS Programs',
    transLifeline:
      '<a href="https://translifeline.org/">Trans Lifeline</a> <i>-</i> call ************',
    trevorProject:
      '<a href="https://www.thetrevorproject.org/get-help/">Trevor Project (LGBTQ+)</a> <i>-</i> call ************, <a href="https://trevorproject.secure.force.com/apex/TrevorChatPreChatForm/">chat online</a>, or text "START" to 678-678',
    viewAllFavorites: 'View All Favorites',
    viewBio: 'View bio',
    whatsNew: "What's New",
    whatWeDo: 'What we do',
    whereWeWork: 'Where we work',
    youHaventFavorited: "You haven't added any favorites yet!",
    yourFavoriteResources: 'Your favorite resources',
  },

  surveys: {
    baselineSurveyDesc:
      'For SMHPs to share with students ahead of the first skills group. Survey will open 9/15.',
    baselineSurveyHeading: 'Student Baseline Survey',
    callInNumber: 'Call in: 1-646-558-8656',
    completedSurveys: 'Completed Surveys',
    completeSurveys: 'Please complete the required surveys below. ',
    currentSurveys: 'Current Surveys',
    followupSurveyDesc:
      'For SMHPs to share with students at the end of the semester. Survey will open 12/15.',
    followupSurveyHeading: 'Student Follow-Up Survey',
    joinSupportCall: 'Join Support Call',
    logCoachingBlockDescCoach:
      "Share your progress! Complete the survey even if you didn't provide any coaching this week.",
    logCoachingBlockDescSmhp: "Don't forget to log this week!",
    logCoachingBlockTitleCoach: "Log this week's coaching",
    logCoachingBlockTitleSmhp: 'Log your delivery',
    logForWeek: 'Log for Week {month}/{day}',
    lookingForSurveys: 'Looking for your full list of surveys?',
    noCurrentPastSurveysDesc:
      'Check back later or keep an eye on your notifications to see when a new survey has been assigned to you. If you think you should have a survey available now, <a href="mailto:<EMAIL>"><strong>please contact us</strong></a>.',
    noCurrentPastSurveysHeading: "You don't have any surveys to complete",
    pastSurveys: 'Past Surveys',
    pastSurveysDesc:
      "Please complete any overdue surveys you're able to accurately log information for. Remember, even if you didn't provide any coaching that week, you should still complete the log and share that information.",
    reviewAllSurveys: 'Review all surveys assigned to you and their completion statuses.',
    smhpLogDesc: 'SMHPs should be completing this log weekly. Survey will open 12/15.',
    smhpLogHeading: 'SMHP Log',
    surveysAsideDesc: 'Quickly find and share the links they need.',
    surveysAsideHeading: 'Surveys for your SMHPs',
    surveysPageDescription:
      'Use this page to keep track of all surveys assigned to you this semester, including your weekly Coaching Log.',
    viewAllSurveys: 'View all surveys',
    yourSurveys: 'Your Surveys',
  },
};
