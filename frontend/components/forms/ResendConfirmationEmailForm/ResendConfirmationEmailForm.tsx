import * as Yup from 'yup';
import React, { useContext, useState } from 'react';
import { SupportSlug, routes } from '@/config/routes';
import { Button } from '../../base/Button/Button';
import { ButtonOrLink } from '@/components/base/ButtonOrLink/ButtonOrLink';
import { Form } from '../Form';
import { Input } from '@/components/base/Input/Input';
import type { ResendConfirmationEmailTypes } from './ResendConfirmationEmailForm.types';
import { UserContext } from '../../../context/user';
import s from './ResendConfirmationEmailForm.module.scss';
import { simpleFormatMessage } from '@/intl/helpers/format';
import { useEnterSubmit } from '@/hooks/useEnterSubmit';
import { useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';

const ResendConfirmationEmailForm = () => {
  const intl = useIntl();
  const { fm } = simpleFormatMessage(intl);

  const formSchema = Yup.object({
    email: Yup.string().email(fm('account.enterValidEmail')).required(fm('account.emailRequired')),
  });

  const form = useForm<ResendConfirmationEmailTypes>({
    mode: 'onTouched',
    resolver: async (data) => {
      try {
        const values = await formSchema.validate(data, { abortEarly: false });
        return { values, errors: {} };
      } catch (error) {
        const yupError = error as Yup.ValidationError;
        return {
          values: {},
          errors: yupError.inner.reduce(
            (acc, err) => ({
              ...acc,
              [err.path!]: { message: err.message },
            }),
            {}
          ),
        };
      }
    },
  });

  const {
    register,
    handleSubmit,
    getValues,
    setValue,
    trigger,
    watch,
    formState: { errors, isSubmitSuccessful },
  } = form;

  const [noEmailFound, setNoEmailFound] = useState(false);
  const [emailConfirmed, setEmailConfirmed] = useState(false);

  const { loading, sendConfirmationEmail } = useContext(UserContext);

  const onSubmit = async (values: ResendConfirmationEmailTypes) => {
    const response = sendConfirmationEmail && (await sendConfirmationEmail(values));
    if (response?.ok) {
      setNoEmailFound(false);
      setEmailConfirmed(false);
    } else if (response?.message === 'user already confirmed') {
      setEmailConfirmed(true);
      trigger('email');
    } else {
      setNoEmailFound(true);
      trigger('email');
    }
  };

  useEnterSubmit(handleSubmit(onSubmit));

  return (
    <div className={s.body}>
      {!noEmailFound && !emailConfirmed && isSubmitSuccessful ? (
        <>
          <div className={s.details}>
            <h2 className={s.header}>{fm('account.emailConfirmation')}</h2>
            <p className={s.guide}>
              {fm('account.clickLink')} <b>{getValues('email')}</b> {fm('account.toFinish')}
            </p>
            <Button className={s.button} href={routes.login()} size={'large'}>
              {fm('account.logIn')}
            </Button>
          </div>
          <div className={s.bottom}>
            <ButtonOrLink className={s.footerLinks} href={routes.login()}>
              {fm('account.returnToLogin')}
            </ButtonOrLink>
            <span className={s.dot}>•</span>
            <ButtonOrLink
              className={s.footerLinks}
              href={routes.support({ slug: SupportSlug.DidntReceiveEmail })}>
              {fm('account.resendAccountConfirmationEmail')}
            </ButtonOrLink>
          </div>
        </>
      ) : (
        <>
          <div className={s.details}>
            <h2 className={s.header}>{fm('account.resendEmailConfirmationLink')}</h2>
            <p>{fm('account.emailConfirmationLinkDetails')}</p>
            <Form onSubmit={handleSubmit(onSubmit)} className={s.form}>
              <Input<ResendConfirmationEmailTypes>
                label={fm('account.emailAddress')}
                className={s.inputWrapper}
                name="email"
                type="email"
                control={{
                  register,
                  watch,
                  setValue,
                }}
                errors={errors}
                errorForced={
                  noEmailFound
                    ? fm('account.noEmailAccount')
                    : emailConfirmed
                    ? fm('account.emailAlreadyConfirmed')
                    : undefined
                }
              />
              <Button className={s.button} type="submit" loading={loading} size={'large'}>
                {loading ? 'Sending...' : 'Send Email Confirmation Link'}
              </Button>
            </Form>
          </div>
          <div className={s.bottom}>
            <ButtonOrLink className={s.footerLinks} href={routes.login()}>
              {fm('account.returnToLogin')}
            </ButtonOrLink>
            <span className={s.dot}>•</span>
            <ButtonOrLink className={s.footerLinks} href={routes.signUp()}>
              {fm('account.signUpInstead')}
            </ButtonOrLink>
          </div>
        </>
      )}
    </div>
  );
};

export { ResendConfirmationEmailForm };
