import { useState } from 'react';
import { ModalSelectButton } from './ModalSelectButton';

export default {
  component: ModalSelectButton,
};

export const Default = () => {
  const [isActive, setIsActive] = useState(false);

  const handleClick = () => {
    setIsActive((prevState) => !prevState);
  };

  return (
    <div>
      <ModalSelectButton label="English" active={isActive} onClick={handleClick} />
    </div>
  );
};
