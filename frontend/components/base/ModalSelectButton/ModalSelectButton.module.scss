.wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
  align-self: stretch;
  padding: 12px;
  border: 1px solid var(--trails-color-mercury);
  border-radius: var(--trails-border-radius);
  background: var(--trails-color-whites);
  cursor: pointer;
  transition: border-color 0.3s;
  transition-property: color, background-color, border-color;

  &:hover {
    border-color: var(--wec-soft-blue-dark);
    background-color: var(--wec-soft-blue-light);

    .checkboxEmptyIconSvg rect {
      stroke: var(--wec-soft-blue-dark);
    }
  }

  &:active {
    border-color: var(--wec-navy-blue);
    background-color: var(--wec-soft-blue-midlight);

    .checkboxEmptyIconSvg rect {
      stroke: var(--wec-navy-blue);
    }
  }
}

.label {
  color: var(--trails-color-black);
  font-weight: 400;
  font-style: normal;
  font-size: 1rem;
  line-height: 120%;
  cursor: pointer;
}

.checkboxEmptyIconSvg {
  rect {
    fill: none;
    stroke: var(--trails-color-mercury);
  }
}

.checkboxFilledIconSvg {
  path {
    fill: var(--wec-navy-blue-dark);
  }
}
