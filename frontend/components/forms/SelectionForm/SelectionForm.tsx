import React, { useEffect, useState, useRef, useCallback } from 'react';
import s from './SelectionForm.module.scss';
import { ModalSelectButton } from '@/components/base/ModalSelectButton/ModalSelectButton';
import type { SelectionFormProps } from './SelectionForm.types';
import { ButtonOrLink } from '@/components/base/ButtonOrLink/ButtonOrLink';

export function SelectionForm({
  title,
  labels = [],
  labelType,
  priorityLabels = [],
  inputOption = false,
  selectAllOption = false,
  selectedOptions = [],
  inputHeader = 'If Other, please describe',
  onSelectionChange,
  onInputChange,
  onSelectAll,
}: SelectionFormProps) {
  const [otherText, setOtherText] = useState<string>('');
  const [selected, setSelected] = useState<(string | number)[]>(selectedOptions);
  const [focusedIndex, setFocusedIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelected(selectedOptions);
  }, [selectedOptions]);

  let restLabels = labels.filter((label) => !priorityLabels.includes(label));

  if (inputOption) {
    restLabels = restLabels.sort((a, b) => {
      if (typeof a === 'string' && typeof b === 'string') {
        return a.localeCompare(b);
      }
      return 0; // Default return value for non-string comparisons
    });
  }

  const sortedLabels = inputOption
    ? [...priorityLabels, ...restLabels, 'Other']
    : [...priorityLabels, ...restLabels];

  // Get all interactive buttons within this component
  const getButtons = useCallback(() => {
    if (!containerRef.current) return [];
    return Array.from(
      containerRef.current.querySelectorAll('[role="button"], button')
    ) as HTMLElement[];
  }, []);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!['ArrowUp', 'ArrowDown', 'Home', 'End', ' ', 'Spacebar', 'Enter'].includes(event.key))
      return;

    const buttons = getButtons();
    if (buttons.length === 0) return;

    let nextIndex = focusedIndex;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        nextIndex = Math.min(focusedIndex + 1, buttons.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        nextIndex = Math.max(focusedIndex - 1, 0);
        break;
      case 'Home':
        event.preventDefault();
        nextIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        nextIndex = buttons.length - 1;
        break;
      case ' ':
      case 'Spacebar':
      case 'Enter':
        event.preventDefault();
        // Trigger click on the currently focused button
        buttons[focusedIndex]?.click();
        return;
    }

    setFocusedIndex(nextIndex);

    // Update tabindex for roving focus
    buttons.forEach((button, index) => {
      button.tabIndex = index === nextIndex ? 0 : -1;
    });

    // Focus the next button
    buttons[nextIndex]?.focus();
  };

  // Set up roving tabindex when component mounts or labels change
  useEffect(() => {
    const buttons = getButtons();
    buttons.forEach((button, index) => {
      button.tabIndex = index === focusedIndex ? 0 : -1;
    });
  }, [sortedLabels, focusedIndex, getButtons]);

  // Handle when user tabs into the button group
  const handleFocus = (event: React.FocusEvent) => {
    const buttons = getButtons();
    const focusedElement = event.target as HTMLElement;
    const newIndex = buttons.findIndex((button) => button === focusedElement);

    if (newIndex !== -1) {
      setFocusedIndex(newIndex);
      // Update tabindex for all buttons
      buttons.forEach((button, index) => {
        button.tabIndex = index === newIndex ? 0 : -1;
      });
    }
  };

  const handleSelect = (label: string | number) => {
    const updatedSelections = selected.includes(label)
      ? selected.filter((option) => option !== label)
      : [...selected, label];

    setSelected(updatedSelections);

    if (onSelectionChange) {
      onSelectionChange(updatedSelections);
    }
  };

  const handleSelectAll = () => {
    const allLabels = sortedLabels.filter((label) => label !== 'Other');
    const updatedSelections = selectedOptions.length === allLabels.length ? [] : allLabels;

    const stringSelections = updatedSelections.map((selection) => selection.toString());

    setSelected(stringSelections);

    if (onSelectionChange) {
      onSelectionChange(stringSelections);
    }

    if (onSelectAll) {
      onSelectAll(stringSelections);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setOtherText(value);

    if (onInputChange) {
      onInputChange(value);
    }
  };

  return (
    <div className={s.wrapper}>
      {title && <h3 className={s.title}>{title}</h3>}
      <div
        ref={containerRef}
        className={s.selectionForm}
        role="group"
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        aria-label={`Select ${labelType}. Use arrow keys to navigate, space or enter to select.`}>
        {sortedLabels.map((label, index) => (
          <ModalSelectButton
            key={index}
            label={label}
            active={selected.includes(label)}
            onClick={() => handleSelect(label)}
            // Add tabIndex prop if ModalSelectButton supports it
            tabIndex={index === focusedIndex ? 0 : -1}
          />
        ))}
      </div>
      {selectAllOption && (
        <ButtonOrLink type="button" className={s.selectAllTextLink} onClick={handleSelectAll}>
          {selectedOptions.length === sortedLabels.length
            ? `Unselect all ${labelType}`
            : `Select all ${labelType}`}
        </ButtonOrLink>
      )}
      {inputOption && selectedOptions.includes('Other') && (
        <div className={s.inputWrapper}>
          <label className={s.inputHeader}>{inputHeader}</label>
          <input className={s.input} type="text" value={otherText} onChange={handleInputChange} />
        </div>
      )}
    </div>
  );
}
