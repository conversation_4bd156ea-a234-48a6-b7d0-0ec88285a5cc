import * as Yup from 'yup';
import React, { useContext, useState } from 'react';
import { Button } from '@/components/base/WecButton/WecButton';
import type { ForgotPasswordFormTypes } from './WecForgotPasswordForm.types';
import { Form } from '@/components/forms/WecForm/WecForm';
import { Input } from '@/components/base/WecInput/WecInput';
import { UserContext } from '../../../context/user';
import { routes, SupportSlug } from '@/config/routes';
import s from './WecForgotPasswordForm.module.scss';
import { simpleFormatMessage } from '@/intl/helpers/format';
import { useEnterSubmit } from '@/hooks/useEnterSubmit';
import { useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { WecLinkUnderline } from '@/components/base/WecLinkUnderline/WecLinkUnderline';
import clsx from 'clsx';

const ForgotPasswordForm = () => {
  const intl = useIntl();
  const { fm } = simpleFormatMessage(intl);

  const formSchema = Yup.object({
    email: Yup.string().email(fm('account.enterValidEmail')).required(fm('account.emailRequired')),
  });

  const form = useForm<ForgotPasswordFormTypes>({
    mode: 'onTouched',
    resolver: async (data) => {
      try {
        const values = await formSchema.validate(data, { abortEarly: false });
        return { values, errors: {} };
      } catch (error) {
        const yupError = error as Yup.ValidationError;
        return {
          values: {},
          errors: yupError.inner.reduce(
            (acc, err) => ({
              ...acc,
              [err.path!]: { message: err.message },
            }),
            {}
          ),
        };
      }
    },
  });

  const {
    register,
    handleSubmit,
    setValue,
    trigger,
    watch,
    formState: { errors, isSubmitSuccessful },
  } = form;

  const [noEmailFound, setNoEmailFound] = useState(false);
  const { loading, forgotPassword } = useContext(UserContext);

  const onSubmit = async (values: ForgotPasswordFormTypes) => {
    const response = forgotPassword && (await forgotPassword(values));
    if (response?.ok) {
      setNoEmailFound(false);
    } else {
      setNoEmailFound(true);
      trigger('email');
    }
  };

  useEnterSubmit(handleSubmit(onSubmit));

  return (
    <div className={clsx('wec-container', s.wrapper)}>
      {!noEmailFound && isSubmitSuccessful ? (
        <>
          <div className={s.successDetails}>
            <span className={'wec-h2'}>{fm('account.passwordRecoveryLinkSent')}</span>
          </div>
          <div className={s.bottom}>
            <WecLinkUnderline href={routes.login()} linkTitle={fm('account.returnToLogin')} />
            <WecLinkUnderline
              href={routes.support({ slug: SupportSlug.DidntReceiveEmail })}
              linkTitle={fm('account.didntReceiveEmail')}
            />
          </div>
        </>
      ) : (
        <>
          <Form
            onSubmit={handleSubmit(onSubmit)}
            beforeForm={
              <h2 className={clsx(s.heading, 'wec-h2')}>{fm('account.forgotYourPassword')}</h2>
            }>
            <div className={'wec-body'}>{fm('account.forgotPasswordDetails')}</div>
            <Input<ForgotPasswordFormTypes>
              label={fm('account.emailAddress')}
              name="email"
              type="email"
              control={{
                register,
                watch,
                setValue,
              }}
              errors={errors}
              errorForced={noEmailFound ? fm('account.noEmailAccount') : undefined}
            />
            <div className={s.button}>
              <Button type="submit" loading={loading} size={'large'}>
                {loading ? 'Sending...' : 'Send Password Recovery Link'}
              </Button>
            </div>
          </Form>
          <div className={s.bottom}>
            <WecLinkUnderline href={routes.login()} linkTitle={fm('account.returnToLogin')} />
            <WecLinkUnderline href={routes.signUp()} linkTitle={fm('account.signUpInstead')} />
          </div>
        </>
      )}
    </div>
  );
};

export { ForgotPasswordForm };
