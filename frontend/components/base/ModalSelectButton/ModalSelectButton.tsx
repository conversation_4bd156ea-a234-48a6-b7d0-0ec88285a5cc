import React from 'react';
import type { ModalSelectButtonProps } from './ModalSelectButton.types';
import s from './ModalSelectButton.module.scss';
import clsx from 'clsx';
import { Icon } from '@/components/base/Icon/Icon';
import { ButtonOrLink } from '../ButtonOrLink/ButtonOrLink';

export function ModalSelectButton({
  classes,
  label,
  active,
  onClick,
  tabIndex,
}: ModalSelectButtonProps) {
  return (
    <ButtonOrLink
      type="button"
      className={clsx(s.wrapper, classes?.wrapper)}
      onClick={onClick}
      aria-pressed={active}
      tabIndex={tabIndex}>
      {active ? (
        <Icon className={s.checkboxFilledIconSvg} symbol="CheckboxFilled" size={32} />
      ) : (
        <Icon className={s.checkboxEmptyIconSvg} symbol="CheckboxEmpty" size={32} />
      )}
      <label className={s.label}>{label}</label>
    </ButtonOrLink>
  );
}
