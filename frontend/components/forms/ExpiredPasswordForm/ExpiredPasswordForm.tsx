import * as Yup from 'yup';
import React, { useContext, useState } from 'react';
import { Button } from '../../base/Button/Button';
import { ButtonOrLink } from '@/components/base/ButtonOrLink/ButtonOrLink';
import type { ExpiredPasswordFormTypes } from './ExpiredPasswordForm.types';
import { Form } from '../Form';
import { Input } from '@/components/base/Input/Input';
import { UserContext } from '../../../context/user';
import { routes, SupportSlug } from '@/config/routes';
import s from './ExpiredPasswordForm.module.scss';
import { simpleFormatMessage } from '@/intl/helpers/format';
import { useEnterSubmit } from '@/hooks/useEnterSubmit';
import { useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';

const ExpiredPasswordForm = () => {
  const intl = useIntl();
  const { fm } = simpleFormatMessage(intl);

  const formSchema = Yup.object({
    email: Yup.string().email(fm('account.enterValidEmail')).required(fm('account.emailRequired')),
  });

  const form = useForm<ExpiredPasswordFormTypes>({
    mode: 'onTouched',
    resolver: async (data) => {
      try {
        const values = await formSchema.validate(data, { abortEarly: false });
        return { values, errors: {} };
      } catch (error) {
        const yupError = error as Yup.ValidationError;
        return {
          values: {},
          errors: yupError.inner.reduce(
            (acc, err) => ({
              ...acc,
              [err.path!]: { message: err.message },
            }),
            {}
          ),
        };
      }
    },
  });

  const {
    register,
    handleSubmit,
    setValue,
    trigger,
    watch,
    formState: { errors, isSubmitSuccessful },
  } = form;

  const [noEmailFound, setNoEmailFound] = useState(false);

  const { forgotPassword } = useContext(UserContext);

  const [loading, setLoading] = useState(false);

  const onSubmit = async (values: any) => {
    setLoading(true);
    const response = forgotPassword && (await forgotPassword(values));
    if (response?.ok) {
      setNoEmailFound(false);
    } else {
      setNoEmailFound(true);
      trigger('email');
    }
    setLoading(false);
  };

  useEnterSubmit(handleSubmit(onSubmit));

  return (
    <div className={s.body}>
      <h2 className={s.header}>{fm('account.expiredPasswordHeader')}</h2>
      {!noEmailFound && isSubmitSuccessful ? (
        <>
          <div className={s.successDetails}>
            <span>{fm('account.passwordRecoveryLinkSent')}</span>
          </div>
          <div className={s.bottom}>
            <ButtonOrLink className={s.footerLinks} href={routes.login()}>
              {fm('account.returnToLogin')}
            </ButtonOrLink>
            <span className={s.dot}>•</span>
            <ButtonOrLink
              className={s.footerLinks}
              href={routes.support({ slug: SupportSlug.DidntReceiveEmail })}>
              {fm('account.didntReceiveEmail')}
            </ButtonOrLink>
          </div>
        </>
      ) : (
        <>
          <Form onSubmit={handleSubmit(onSubmit)}>
            <div className={s.details}>{fm('account.expiredPasswordDetails')}</div>

            <Input<ExpiredPasswordFormTypes>
              label={fm('account.emailAddress')}
              name="email"
              type="email"
              control={{
                register,
                watch,
                setValue,
              }}
              errors={errors}
              errorForced={noEmailFound ? fm('account.noEmailAccount') : undefined}
            />

            <div className={s.button}>
              <Button type="submit" loading={loading} size={'large'}>
                {loading ? 'Sending...' : 'Send Password Recovery Link'}
              </Button>
            </div>
          </Form>
          <div className={s.bottom}>
            <ButtonOrLink className={s.footerLinks} href={routes.login()}>
              {fm('account.returnToLogin')}
            </ButtonOrLink>
            <span className={s.dot}>•</span>
            <ButtonOrLink className={s.footerLinks} href={routes.signUp()}>
              {fm('account.signUpInstead')}
            </ButtonOrLink>
          </div>
        </>
      )}
    </div>
  );
};

export { ExpiredPasswordForm };
